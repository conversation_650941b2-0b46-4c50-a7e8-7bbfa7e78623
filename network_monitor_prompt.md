# Windows Network Monitor Application - Comprehensive Development Prompt

Create a Windows desktop application for comprehensive network monitoring with the following specifications:

## Core Requirements

### Application Framework
- Use **C# with WinForms or WPF** for maximum Windows compatibility (Windows 7, 8, 10, 11)
- Alternative: **Python with tkinter/<PERSON>yQt** if cross-version compatibility is priority
- Target .NET Framework 4.7.2 or higher for broad compatibility

### Main Features to Implement

#### 1. Network Connection Monitoring
- **Real-time connection tracking**: Display all active TCP/UDP connections
- **Connection details**: Show local/remote IP addresses, ports, protocol type, and connection state
- **Process identification**: Map each connection to its corresponding application/process
- **Connection history**: Log connection attempts and closures with timestamps
- **Filtering options**: Filter by protocol (TCP/UDP), port ranges, or specific applications

#### 2. Bandwidth Monitoring
- **Real-time speed display**: Show current upload/download speeds in Mbps/Kbps
- **Historical graphs**: Plot bandwidth usage over time (last hour, day, week)
- **Peak usage tracking**: Record and display maximum speeds achieved
- **Network interface selection**: Support multiple network adapters
- **Data usage totals**: Track cumulative data transferred per session/day/month

#### 3. Per-Application Network Usage
- **Process-level monitoring**: Track network usage by individual applications
- **Top consumers list**: Rank applications by data usage
- **Application details**: Show executable path, PID, and network activity patterns
- **Usage alerts**: Notify when applications exceed defined thresholds
- **Historical per-app data**: Store and display usage trends for each application

#### 4. System Integration Features
- **Windows service mode**: Option to run as background service
- **System tray integration**: Minimize to system tray with quick stats
- **Startup integration**: Auto-start with Windows option
- **Administrative privileges**: Handle UAC requirements for deep system access

## Technical Implementation Requirements

### Windows APIs to Utilize
```csharp
// Key APIs and libraries to implement:
- GetTcp6Table2() / GetTcpTable2() for TCP connections
- GetUdp6Table() / GetUdpTable() for UDP connections  
- GetExtendedTcpTable() for process-to-connection mapping
- PerformanceCounter classes for network interface statistics
- WMI (Windows Management Instrumentation) for additional system data
- iphlpapi.dll functions for low-level network data
```

### Data Collection Methods
- **Performance Counters**: Use System.Diagnostics.PerformanceCounter for bandwidth data
- **WMI Queries**: Query Win32_PerfRawData_Tcpip_NetworkInterface for interface stats
- **Native API calls**: P/Invoke calls to Windows networking APIs
- **Registry monitoring**: Track network-related registry changes
- **ETW (Event Tracing for Windows)**: For advanced network event tracking

### User Interface Design
- **Main dashboard**: Overview of current network status and top statistics
- **Connections tab**: Detailed list of all active connections with sorting/filtering
- **Bandwidth tab**: Real-time graphs and historical bandwidth data
- **Applications tab**: Per-process network usage breakdown
- **Settings tab**: Configuration options, thresholds, and preferences
- **Alerts panel**: Notification area for network events and warnings

## Advanced Features to Include

### Security Monitoring
- **Suspicious connection detection**: Flag unusual outbound connections
- **Port scan detection**: Identify potential port scanning attempts  
- **Whitelist/blacklist management**: Allow/block specific applications or connections
- **Connection logging**: Detailed logs for security analysis

### Data Export and Reporting
- **Export capabilities**: Save data to CSV, JSON, or XML formats
- **Scheduled reports**: Generate periodic network usage reports
- **Custom time ranges**: Analyze data for specific date/time periods
- **Integration APIs**: Provide data export for other monitoring tools

### Performance Optimization
- **Efficient polling**: Optimize data collection intervals to minimize system impact
- **Memory management**: Handle large datasets without memory leaks
- **Multi-threading**: Use background threads for data collection
- **Data compression**: Compress historical data for storage efficiency

## Platform Compatibility Requirements

### Windows Version Support
- **Windows 7 SP1**: Minimum supported version
- **Windows 8/8.1**: Full feature compatibility
- **Windows 10/11**: Latest features and optimizations
- **32-bit and 64-bit**: Support both architectures

### Permission Requirements
- **Administrator privileges**: Required for complete network monitoring
- **UAC handling**: Proper elevation requests and handling
- **Service installation**: Ability to install/uninstall Windows service
- **Firewall integration**: Respect Windows Firewall settings

## Code Structure and Architecture

### Recommended Project Structure
```
NetworkMonitor/
├── Core/
│   ├── NetworkCollector.cs      # Main data collection engine
│   ├── ConnectionMonitor.cs     # TCP/UDP connection tracking
│   ├── BandwidthMonitor.cs      # Speed and usage monitoring
│   └── ProcessMapper.cs         # Process-to-connection mapping
├── UI/
│   ├── MainWindow.cs           # Primary application window
│   ├── ConnectionsView.cs      # Connections display component
│   ├── BandwidthView.cs        # Bandwidth graphs and stats
│   └── SettingsWindow.cs       # Configuration interface
├── Services/
│   ├── WindowsService.cs       # Background service implementation
│   └── NotificationService.cs  # System tray and alerts
└── Utils/
    ├── NativeAPI.cs           # Windows API P/Invoke declarations
    ├── DataStorage.cs         # Historical data management
    └── ConfigManager.cs       # Settings and preferences
```

### Error Handling Requirements
- **Graceful degradation**: Continue operation even if some features fail
- **Comprehensive logging**: Log all errors and important events
- **User feedback**: Clear error messages and recovery suggestions
- **Automatic recovery**: Restart failed monitoring components automatically

## Testing and Deployment

### Testing Requirements
- **Multi-version testing**: Test on different Windows versions
- **Performance testing**: Ensure minimal system resource usage
- **Long-running stability**: Test 24/7 operation without issues
- **Permission testing**: Verify behavior with different user privilege levels

### Deployment Considerations
- **Installer creation**: MSI or executable installer with proper dependencies
- **Digital signing**: Code signing for Windows security compliance
- **Update mechanism**: Built-in update checking and installation
- **Uninstall cleanup**: Complete removal of all components and data

## Additional Implementation Notes

### Configuration Options
- **Update intervals**: Configurable refresh rates for different data types
- **Data retention**: Customizable historical data storage periods
- **Alert thresholds**: User-defined limits for bandwidth and usage warnings
- **UI preferences**: Customizable interface layouts and themes

### Documentation Requirements
- **User manual**: Comprehensive guide for all features
- **Technical documentation**: API references and architecture details
- **Troubleshooting guide**: Common issues and solutions
- **Installation guide**: Step-by-step setup instructions

Generate a complete, production-ready Windows application that implements all these features with clean, well-documented code, proper error handling, and a professional user interface suitable for both home users and IT professionals.